{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "inertiajs/inertia-laravel": "^1.0", "laravel/framework": "^11.31", "laravel/jetstream": "^5.3", "laravel/reverb": "^1.4", "laravel/sanctum": "^4.0", "laravel/scout": "^10.11", "laravel/socialite": "^5.18", "laravel/tinker": "^2.9", "spatie/laravel-data": "^4.11", "spatie/laravel-permission": "^6.16", "spatie/laravel-ray": "^1.40", "tightenco/ziggy": "^2.0"}, "require-dev": {"deployer/deployer": "^7.5", "fakerphp/faker": "^1.23", "laravel/pint": "^1.18", "laravel/sail": "^1.40", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.1", "phpunit/phpunit": "^11.0.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan reverb:start\"  \"php artisan pail --timeout=0\" \"npm run dev\"   --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}