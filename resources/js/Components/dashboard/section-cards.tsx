import { TrendingDownIcon, TrendingUpIcon, Setting<PERSON>, Target } from 'lucide-react';

import { Badge } from '@/Components/ui/badge';
import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/Components/ui/card';
import React, { useEffect, useState } from 'react';
import axios from 'axios';
interface AutomationStats {
  total_rules: number;
  active_rules: number;
  total_matches: number;
  recent_matches: number;
}

interface Props {
  totalPosts: number;
  totalUsers: number;
  automationStats?: AutomationStats;
}
export function SectionCards({ totalPosts, totalUsers, automationStats }: Props) {
  return (
    <div
      className="*:data-[slot=card]:shadow-xs @xl/main:grid-cols-2 @5xl/main:grid-cols-4 grid grid-cols-1 gap-4 px-4 *:data-[slot=card]:bg-gradient-to-t
         *:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card lg:px-6"
    >
      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription>Total Question and Feature </CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {totalPosts}
          </CardTitle>
          <div className="absolute right-4 top-4">
            {/*<Badge variant="outline" className="flex gap-1 rounded-lg text-xs">*/}
            {/*    <TrendingUpIcon className="size-3" />*/}
            {/*    +12.5%*/}
            {/*</Badge>*/}
          </div>
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            Trending up this month <TrendingUpIcon className="size-4" />
          </div>
          <div className="text-muted-foreground">
            Visitors for the last 6 months
          </div>
        </CardFooter>
      </Card>
      <Card className="@container/card">
        <CardHeader className="relative">
          <CardDescription>Total Users</CardDescription>
          <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
            {totalUsers}
          </CardTitle>
          {/*<div className="absolute right-4 top-4">*/}
          {/*  <Badge variant="outline" className="flex gap-1 rounded-lg text-xs">*/}
          {/*    <TrendingDownIcon className="size-3" />*/}
          {/*    -20%*/}
          {/*  </Badge>*/}
          {/*</div>*/}
        </CardHeader>
        <CardFooter className="flex-col items-start gap-1 text-sm">
          <div className="line-clamp-1 flex gap-2 font-medium">
            We have {totalUsers} users
          </div>
          {/*<div className="text-muted-foreground">*/}
          {/*  Acquisition needs attention*/}
          {/*</div>*/}
        </CardFooter>
      </Card>

      {/* Automation Stats Cards */}
      {automationStats && (
        <>
          <Card className="@container/card">
            <CardHeader className="relative">
              <CardDescription>Active Automation Rules</CardDescription>
              <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
                {automationStats.active_rules}
              </CardTitle>
              <div className="absolute right-4 top-4">
                <Settings className="size-5 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                {automationStats.total_rules} total rules
              </div>
              <div className="text-muted-foreground">
                Automating ticket processing
              </div>
            </CardFooter>
          </Card>

          <Card className="@container/card">
            <CardHeader className="relative">
              <CardDescription>Automation Matches</CardDescription>
              <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">
                {automationStats.recent_matches}
              </CardTitle>
              <div className="absolute right-4 top-4">
                <Target className="size-5 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardFooter className="flex-col items-start gap-1 text-sm">
              <div className="line-clamp-1 flex gap-2 font-medium">
                {automationStats.total_matches} total matches
              </div>
              <div className="text-muted-foreground">
                Last 7 days activity
              </div>
            </CardFooter>
          </Card>
        </>
      )}

      {/*<Card className="@container/card">*/}
      {/*    <CardHeader className="relative">*/}
      {/*        <CardDescription>Active Accounts</CardDescription>*/}
      {/*        <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">45,678</CardTitle>*/}
      {/*        <div className="absolute right-4 top-4">*/}
      {/*            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs">*/}
      {/*                <TrendingUpIcon className="size-3" />*/}
      {/*                +12.5%*/}
      {/*            </Badge>*/}
      {/*        </div>*/}
      {/*    </CardHeader>*/}
      {/*    <CardFooter className="flex-col items-start gap-1 text-sm">*/}
      {/*        <div className="line-clamp-1 flex gap-2 font-medium">*/}
      {/*            Strong user retention <TrendingUpIcon className="size-4" />*/}
      {/*        </div>*/}
      {/*        <div className="text-muted-foreground">Engagement exceed targets</div>*/}
      {/*    </CardFooter>*/}
      {/*</Card>*/}
      {/*<Card className="@container/card">*/}
      {/*    <CardHeader className="relative">*/}
      {/*        <CardDescription>Growth Rate</CardDescription>*/}
      {/*        <CardTitle className="@[250px]/card:text-3xl text-2xl font-semibold tabular-nums">4.5%</CardTitle>*/}
      {/*        <div className="absolute right-4 top-4">*/}
      {/*            <Badge variant="outline" className="flex gap-1 rounded-lg text-xs">*/}
      {/*                <TrendingUpIcon className="size-3" />*/}
      {/*                +4.5%*/}
      {/*            </Badge>*/}
      {/*        </div>*/}
      {/*    </CardHeader>*/}
      {/*    <CardFooter className="flex-col items-start gap-1 text-sm">*/}
      {/*        <div className="line-clamp-1 flex gap-2 font-medium">*/}
      {/*            Steady performance <TrendingUpIcon className="size-4" />*/}
      {/*        </div>*/}
      {/*        <div className="text-muted-foreground">Meets growth projections</div>*/}
      {/*    </CardFooter>*/}
      {/*</Card>*/}
    </div>
  );
}
