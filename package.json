{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@prettier/plugin-php": "^0.22.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/postcss": "^4.1.4", "@tailwindcss/typography": "^0.5.15", "@types/lodash": "^4.17.13", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.7.9", "concurrently": "^9.0.1", "laravel-echo": "^1.19.0", "laravel-vite-plugin": "^1.1.1", "postcss": "^8.4.49", "prettier": "^3.4.2", "pusher-js": "^8.4.0", "tailwindcss": "^4.1.4", "typescript": "^5.7.2", "vite": "^5.4.11"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@inertiajs/inertia": "^0.11.1", "@inertiajs/react": "^1.3.0", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-table": "^8.21.2", "@tiptap/extension-heading": "^2.10.4", "@tiptap/extension-link": "^2.10.4", "@tiptap/react": "^2.10.4", "@tiptap/starter-kit": "^2.10.4", "@types/node": "^24.0.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "emoji-picker-react": "^4.12.0", "framer-motion": "^12.3.1", "lodash": "^4.17.21", "lucide-react": "^0.469.0", "motion": "^12.3.1", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^9.6.2", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-infinite-scroll-component": "^6.1.0", "react-quill": "^2.0.0", "react-resizable-panels": "^2.1.7", "react-swipeable": "^7.0.2", "recharts": "^2.15.1", "sonner": "^2.0.5", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "vaul": "^1.1.2", "ziggy-js": "^2.4.1", "zod": "^3.24.2", "zustand": "^5.0.3"}, "prettier": {"semi": true, "singleQuote": true, "useTabs": false, "tabWidth": 2, "trailingComma": "all", "printWidth": 80, "arrowParens": "avoid"}}